# PRO_TYPE_NUM_MAP和fileTemplates数据关系重构

## 重构概述

### 🎯 **重构目标**
将硬编码的`PRO_TYPE_NUM_MAP`重构为动态从`fileTemplates`计算文件数量，实现数据一致性和自动同步。

### 🔍 **问题分析**

#### 重构前的问题：
1. **数据重复维护**：
   - `PRO_TYPE_NUM_MAP`（第3101-3108行）硬编码文件数量
   - `fileTemplates`（第695-797行）定义文件模板
   - 两者需要手动同步，容易出现不一致

2. **维护成本高**：
   - 当`fileTemplates`中的文件模板变化时，需要手动更新`PRO_TYPE_NUM_MAP`
   - 容易遗漏更新，导致数据不一致

3. **特殊情况处理**：
   - "华宇高压用户"通过引用复用"高压用户"模板（第797行）
   - 需要确保这种引用关系在动态计算中正确处理

## 重构实现

### ✅ **核心函数重构**

#### 1. **动态映射生成函数**
```javascript
function generateProjectTypeNumMap() {
    const map = {};
    
    // 遍历fileTemplates对象，计算每个项目类型的文件数量
    Object.keys(fileTemplates).forEach(projectType => {
        const template = fileTemplates[projectType];
        if (Array.isArray(template)) {
            map[projectType] = template.length;
        }
    });
    
    return map;
}
```

#### 2. **重构后的getRequiredFileCount函数**
```javascript
function getRequiredFileCount(projectType) {
    // 动态从fileTemplates计算文件数量，确保数据一致性
    const PRO_TYPE_NUM_MAP = generateProjectTypeNumMap();
    
    const result = PRO_TYPE_NUM_MAP[projectType] || null;
    
    // 调试信息：验证重构后的数量与原硬编码数量是否一致
    if (window.DEBUG_MODE) {
        const originalMap = {
            '高压用户': 17,
            '低压用户': 10,
            '光伏低压自然人': 18,
            '光伏低压非自然人': 20,
            '光伏高压': 20,
            '华宇高压用户': 17
        };
        
        if (originalMap[projectType] !== result) {
            console.warn(`项目类型 "${projectType}" 的文件数量不一致: 原=${originalMap[projectType]}, 新=${result}`);
        }
    }
    
    return result;
}
```

#### 3. **数据一致性验证函数**
```javascript
function validateProjectTypeNumMapping() {
    const originalMap = {
        '高压用户': 17,
        '低压用户': 10,
        '光伏低压自然人': 18,
        '光伏低压非自然人': 20,
        '光伏高压': 20,
        '华宇高压用户': 17
    };
    
    const dynamicMap = generateProjectTypeNumMap();
    const inconsistencies = [];
    
    // 检查每个项目类型的数量是否一致
    Object.keys(originalMap).forEach(projectType => {
        const originalCount = originalMap[projectType];
        const dynamicCount = dynamicMap[projectType];
        
        if (originalCount !== dynamicCount) {
            inconsistencies.push({
                projectType,
                originalCount,
                dynamicCount,
                difference: dynamicCount - originalCount
            });
        }
    });
    
    // 检查是否有新增的项目类型
    Object.keys(dynamicMap).forEach(projectType => {
        if (!originalMap.hasOwnProperty(projectType)) {
            inconsistencies.push({
                projectType,
                originalCount: 0,
                dynamicCount: dynamicMap[projectType],
                difference: dynamicMap[projectType],
                isNew: true
            });
        }
    });
    
    return {
        isConsistent: inconsistencies.length === 0,
        inconsistencies,
        originalMap,
        dynamicMap
    };
}
```

### ✅ **自动验证机制**

在页面加载时自动验证数据一致性：
```javascript
// 验证项目类型文件数量映射的一致性
const validation = validateProjectTypeNumMapping();
if (!validation.isConsistent) {
    console.warn('⚠️ 项目类型文件数量映射存在不一致:', validation.inconsistencies);
    console.table(validation.inconsistencies);
} else {
    console.log('✅ 项目类型文件数量映射验证通过');
}

// 输出当前的映射表供参考
console.log('📊 当前项目类型文件数量映射:', validation.dynamicMap);
```

## 预期验证结果

### 📊 **文件数量对比**

根据`fileTemplates`的定义，预期的文件数量应该是：

| 项目类型 | 原硬编码数量 | 动态计算数量 | 状态 |
|---------|-------------|-------------|------|
| 高压用户 | 17 | 17 | ✅ 一致 |
| 低压用户 | 10 | 10 | ✅ 一致 |
| 光伏低压自然人 | 18 | 18 | ✅ 一致 |
| 光伏低压非自然人 | 20 | 20 | ✅ 一致 |
| 光伏高压 | 20 | 20 | ✅ 一致 |
| 华宇高压用户 | 17 | 17 | ✅ 一致 |

### 🔍 **特殊情况验证**

**华宇高压用户引用处理**：
- 第797行：`fileTemplates.华宇高压用户=fileTemplates.高压用户;`
- 这种引用方式使得"华宇高压用户"和"高压用户"共享相同的文件模板数组
- 动态计算应该正确识别这种引用关系，返回相同的文件数量（17个）

## 重构优势

### ✅ **数据一致性**
- 消除了手动同步的需要
- 确保`PRO_TYPE_NUM_MAP`始终与`fileTemplates`保持一致

### ✅ **维护简化**
- 只需维护`fileTemplates`一个数据源
- 文件模板变更时无需二次修改

### ✅ **错误预防**
- 自动验证机制可以及时发现数据不一致
- 调试模式下提供详细的差异信息

### ✅ **扩展性**
- 新增项目类型时只需在`fileTemplates`中定义
- 自动支持动态文件数量计算

## 测试验证步骤

### 1. **控制台验证**
1. 打开浏览器开发者工具
2. 查看控制台输出的验证结果
3. 确认所有项目类型的文件数量一致

### 2. **功能测试**
1. 测试文件上传功能是否正常
2. 测试项目创建功能是否正常
3. 验证文件数量限制是否正确生效

### 3. **边界测试**
1. 测试"华宇高压用户"的文件数量是否正确
2. 测试不存在的项目类型是否返回null
3. 验证文件模板变更后数量是否自动更新

## 注意事项

1. **服务器端同步**：需要确保服务器端的`PRO_TYPE_NUM_MAP`也进行相应更新
2. **缓存清理**：重构后建议清除浏览器缓存重新测试
3. **向后兼容**：保留原有的验证逻辑，确保重构不影响现有功能

## 相关文件
- `myjs.js`: 主要重构文件
- `app.py`: 服务器端对应的映射定义（需要同步更新）
