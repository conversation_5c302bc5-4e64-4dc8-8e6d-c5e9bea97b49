<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目类型映射重构测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 项目类型映射重构测试</h1>
        
        <div class="test-section">
            <h3>📊 数据一致性验证</h3>
            <p>验证重构后的动态映射与原硬编码映射是否一致</p>
            <button onclick="runConsistencyTest()">运行一致性测试</button>
            <div id="consistency-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 详细映射对比</h3>
            <p>对比每个项目类型的文件数量</p>
            <button onclick="showDetailedComparison()">显示详细对比</button>
            <div id="detailed-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 特殊情况测试</h3>
            <p>测试"华宇高压用户"引用"高压用户"的情况</p>
            <button onclick="testSpecialCases()">测试特殊情况</button>
            <div id="special-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>⚡ 性能测试</h3>
            <p>测试动态计算的性能</p>
            <button onclick="runPerformanceTest()">运行性能测试</button>
            <div id="performance-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 控制台输出</h3>
            <p>查看浏览器控制台获取详细的测试信息</p>
            <button onclick="openConsole()">打开控制台</button>
        </div>
    </div>

    <!-- 引入重构后的JS文件 -->
    <script src="myjs.js"></script>
    
    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 项目类型映射重构测试页面已加载');
            
            // 自动运行基础验证
            setTimeout(() => {
                runConsistencyTest();
            }, 1000);
        });

        function runConsistencyTest() {
            const resultDiv = document.getElementById('consistency-result');
            
            try {
                // 检查函数是否存在
                if (typeof validateProjectTypeNumMapping !== 'function') {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 错误：validateProjectTypeNumMapping 函数未找到';
                    return;
                }

                const validation = validateProjectTypeNumMapping();
                
                if (validation.isConsistent) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 测试通过：所有项目类型的文件数量一致\n\n' +
                        '原始映射：\n' + JSON.stringify(validation.originalMap, null, 2) + '\n\n' +
                        '动态映射：\n' + JSON.stringify(validation.dynamicMap, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 测试失败：发现数据不一致\n\n' +
                        '不一致项目：\n' + JSON.stringify(validation.inconsistencies, null, 2);
                }
                
                console.log('📊 一致性测试结果:', validation);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 测试执行错误：' + error.message;
                console.error('一致性测试错误:', error);
            }
        }

        function showDetailedComparison() {
            const resultDiv = document.getElementById('detailed-result');
            
            try {
                const validation = validateProjectTypeNumMapping();
                
                let tableHTML = `
                    <table>
                        <thead>
                            <tr>
                                <th>项目类型</th>
                                <th>原硬编码数量</th>
                                <th>动态计算数量</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                Object.keys(validation.originalMap).forEach(projectType => {
                    const originalCount = validation.originalMap[projectType];
                    const dynamicCount = validation.dynamicMap[projectType];
                    const isConsistent = originalCount === dynamicCount;
                    
                    tableHTML += `
                        <tr>
                            <td>${projectType}</td>
                            <td>${originalCount}</td>
                            <td>${dynamicCount || 'N/A'}</td>
                            <td class="${isConsistent ? 'status-ok' : 'status-error'}">
                                ${isConsistent ? '✅ 一致' : '❌ 不一致'}
                            </td>
                        </tr>
                    `;
                });
                
                tableHTML += '</tbody></table>';
                resultDiv.innerHTML = tableHTML;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 错误：${error.message}</div>`;
            }
        }

        function testSpecialCases() {
            const resultDiv = document.getElementById('special-result');
            
            try {
                const huayuCount = getRequiredFileCount('华宇高压用户');
                const gaoyaCount = getRequiredFileCount('高压用户');
                
                let result = `华宇高压用户文件数量: ${huayuCount}\n`;
                result += `高压用户文件数量: ${gaoyaCount}\n\n`;
                
                if (huayuCount === gaoyaCount && huayuCount === 17) {
                    result += '✅ 特殊情况测试通过：华宇高压用户正确引用高压用户模板';
                    resultDiv.className = 'result success';
                } else {
                    result += '❌ 特殊情况测试失败：华宇高压用户引用异常';
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
                console.log('🔍 特殊情况测试:', { huayuCount, gaoyaCount });
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 特殊情况测试错误：' + error.message;
            }
        }

        function runPerformanceTest() {
            const resultDiv = document.getElementById('performance-result');
            
            try {
                const iterations = 10000;
                const projectTypes = ['高压用户', '低压用户', '光伏低压自然人', '光伏低压非自然人', '光伏高压', '华宇高压用户'];
                
                // 测试动态计算性能
                const startTime = performance.now();
                
                for (let i = 0; i < iterations; i++) {
                    projectTypes.forEach(type => {
                        getRequiredFileCount(type);
                    });
                }
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const avgTime = totalTime / (iterations * projectTypes.length);
                
                let result = `性能测试结果：\n`;
                result += `总迭代次数: ${iterations * projectTypes.length}\n`;
                result += `总耗时: ${totalTime.toFixed(2)} ms\n`;
                result += `平均每次调用: ${avgTime.toFixed(4)} ms\n\n`;
                
                if (avgTime < 0.1) {
                    result += '✅ 性能测试通过：动态计算性能良好';
                    resultDiv.className = 'result success';
                } else {
                    result += '⚠️ 性能警告：动态计算可能需要优化';
                    resultDiv.className = 'result warning';
                }
                
                resultDiv.textContent = result;
                
                console.log('⚡ 性能测试结果:', { totalTime, avgTime, iterations });
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 性能测试错误：' + error.message;
            }
        }

        function openConsole() {
            alert('请按 F12 或右键选择"检查"打开开发者工具，然后查看 Console 标签页');
            
            // 在控制台输出测试信息
            console.log('🔧 项目类型映射重构测试');
            console.log('使用 testProjectTypeMapping() 函数进行完整测试');
            
            if (typeof window.testProjectTypeMapping === 'function') {
                window.testProjectTypeMapping();
            }
        }
    </script>
</body>
</html>
